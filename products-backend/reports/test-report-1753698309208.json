{"timestamp": "2025-07-28T10:25:09.206Z", "config": {"apiBaseUrl": "http://localhost:3000/api/v1", "minioBaseUrl": "http://*************:9000", "testProductId": "rec12345abcd", "timeout": 30000}, "stats": {"total": 9, "passed": 5, "failed": 4, "errors": [{"test": "API服务连接", "error": ""}, {"test": "产品图片API", "error": ""}, {"test": "图片一致性检查API", "error": ""}, {"test": "图片修复功能", "error": ""}]}, "environment": {"nodeVersion": "v24.3.0", "platform": "darwin", "mongoUri": "***********************************************************************"}}